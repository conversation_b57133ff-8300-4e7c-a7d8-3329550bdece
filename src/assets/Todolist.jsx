import { useState } from "react";
import "./Todolist.css";

function Todolist() {
    const [tasks, setTasks] = useState([]);

    const [input, setInput] = useState("");


    const deleteTask=(indexToDelete)=>{
        setTasks(tasks.filter((task,index)=>index!==indexToDelete))


    };

    const handSubmit = (e) => {
        e.preventDefault();
        if (input.trim() === "") return;
        setTasks([...tasks, { text: input, completed: false }]);
        setInput("");
    };
    const toggleComplete = (indexToToggle) => {
        setTasks(
            tasks.map((task, index) => {
                if (index === indexToToggle) {
                    return { ...task, completed: !task.completed };
                }
                return task;
            })
        );
    };

    const saveTasks = tasks.map((task, index) => {
        return (
            <li key={index}>
                <input
                    type="checkbox"
                    checked={task.completed}
                    onChange={() => toggleComplete(index)}
                />
                <span className={task.completed ? 'completed' : ''}>
                    {task.text}
                </span>
                <button onClick={() => deleteTask(index)}>Delete</button>
            </li>
        );
    });


    return (
        <div>
            <h1 className="title"> To Do List </h1>

            <form onSubmit={handSubmit}>
                <input
                    className="input"
                    type="text"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                />
                <button type="submit">Add</button>
            </form>
            <div className="save-task">
                <ul>
                    {saveTasks}
                </ul>

            </div>
        </div>
    );
}
export default Todolist;
