import { useState } from 'react';
import './Todolist.css';




function Todolist (){
    const [Tasks,setTasks]=useState([]);
    const [input,setinput]=useState('');

    const handSubmit=(e)=>{
        e.preventDefault();
        if(input.trim === "") return;
        setTasks([...Tasks,{Text:input,completed:false}]);
        setinput('');

    
    
    }  


    return(
        <div>
            <h1 className="title"> To Do List </h1>
            
            <form onSubmit={handSubmit}>
                
                <input
                className='input'
                type='text'
                value={input}
                onChange={(e)=>setinput(e.target.value)}
                
                />
                <button type="submit">Add</button>
            
            </form>
        </div>

    );
}
export default Todolist ;